part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class LoginEvent extends AuthEvent {
  final String carteira;

  const LoginEvent({required this.carteira});
}

class VerifyAnswerEvent extends AuthEvent {
  final String userAnswer;
  final String correctAnswer;
  final String redirectTo;
  final String cardlogged;

  const VerifyAnswerEvent(
      {required this.userAnswer,
      required this.correctAnswer,
      required this.redirectTo,
       required this.cardlogged});
}

class TestEvent extends AuthEvent {
  const TestEvent();
}
