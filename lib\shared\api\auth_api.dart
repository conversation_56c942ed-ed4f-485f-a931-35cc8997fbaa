import 'dart:async';
import 'dart:convert';
import 'package:pavirtual_tablet/shared/flavor_config.dart';
import 'package:pa_virtual/shared/exceptions.dart';
import 'package:pavirtual_tablet/model/pa_virtual_login.vo.dart';
import 'package:pavirtual_tablet/shared/http_client.dart';
import 'package:pavirtual_tablet/shared/logger_print.dart';
import 'package:pavirtual_tablet/shared/utils/messages.exceptions.dart';

class AuthApi {
  final UnimedHttpClient httpClient;
  final attributeCredentials = 'cookie-session';
  final passwordCredentials = '357538782F413F4428472B4B62506553';

  String? _tokenPerfilApps;

  final logger = UnimedLogger(className: 'AuthApi');

  AuthApi(
    this.httpClient,
  );

  Future<String> tokenPerfilApps({String? user, String? password}) async {
    if (!_isValidTokenPerfilApps(_tokenPerfilApps)) {
      user ??= FlavorConfig.instance!.values.profilePermissions.user;
      password ??= FlavorConfig.instance!.values.profilePermissions.password;

      try {
        final String url =
            '${FlavorConfig.instance!.values.profilePermissions.url}auth/login';
        final body = jsonEncode({"user": user, "password": password});

        final headers = {"Content-Type": "application/json"};

        final response =
            await httpClient.post(Uri.parse(url), body: body, headers: headers);

        logger.d(
            'AuthApi tokenPerfilApps user ${FlavorConfig.instance!.values.profilePermissions.user}');
        logger.d(
            'AuthApi tokenPerfilApps pass ${FlavorConfig.instance!.values.profilePermissions.password.length}');

        if (response.statusCode == 200) {
          logger.d('AuthApi tokenPerfilApps success logged');
          _tokenPerfilApps = (jsonDecode(response.body))['token'];
          logger.d('AuthApi tokenPerfilApps token $_tokenPerfilApps');
          return _tokenPerfilApps!;
        } else {
          logger.e('AuthApi error status != 200 ${response.body}');
          throw ProfilesException(
              'AuthApi tokenPerfilApps != 200 => ${response.statusCode}');
        }
      } catch (e) {
        logger.e('AuthApi tokenPerfilApps Exception $e');
        throw ProfilesException('Erro ao carregar token ${e.toString()}');
      }
    } else {
      return _tokenPerfilApps!;
    }
  }

  Future<String?> getProfileToken() async {
    try {
      final String url =
          '${FlavorConfig.instance!.values.profilePermissions.url}auth/login';
      final Map<String, String> body = {
        'user': FlavorConfig.instance!.values.profilePermissions.user,
        'password': FlavorConfig.instance!.values.profilePermissions.password,
      };

      final response =
          await httpClient.post(Uri.parse(url), body: jsonEncode(body));

      logger.d('getProfileToken response.body ${response.body}');

      if (response.statusCode == 200) {
        logger.d('getProfileToken success logged');
        final result = jsonDecode(response.body);
        return result['token'];
      } else {
        logger.e('getProfileToken error status != 200 ${response.body}');
        throw ProfilesException('Falha ao carregar permissões do perfil');
      }
    } on NoInternetException catch (ex) {
      logger.e('getProfileToken NoInternetException $ex');
      rethrow;
    } catch (e) {
      logger.e('getProfileToken Exception $e');
      throw ProfilesException('Falha ao carregar permissões do perfil');
    }
  }

  bool _isValidTokenPerfilApps(String? token) {
    if (_tokenPerfilApps == null || _tokenPerfilApps!.isEmpty) {
      return false;
    } else {
      final parts = token!.split('.');
      if (parts.length != 3) {
        throw Exception('invalid token');
      }

      final payload = _decodeBase64(parts[1]);
      final payloadMap = json.decode(payload);
      if (payloadMap is! Map<String, dynamic>) {
        throw Exception('invalid payload');
      }

      final DateTime exp =
          DateTime.fromMillisecondsSinceEpoch(payloadMap['exp']);
      return exp.isAfter(DateTime.now());
    }
  }

  String _decodeBase64(String str) {
    String output = str.replaceAll('-', '+').replaceAll('_', '/');

    switch (output.length % 4) {
      case 0:
        break;
      case 2:
        output += '==';
        break;
      case 3:
        output += '=';
        break;
      default:
        throw Exception('Illegal base64url string!"');
    }

    return utf8.decode(base64Url.decode(output));
  }

  Future<List<LoginPAVirtualVO>> login({required String cpfOrCart}) async {
    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}pa/app/login/$cpfOrCart';

      

      final token = await getProfileToken();

      final headers = {
        'Authorization': "Bearer $token",
        'Content-Type': 'application/json',
      };
     
      final response = await httpClient.get(Uri.parse(url), headers: headers);
      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        logger.i('login success $data');

        final listLoginPAVirtualVO = <LoginPAVirtualVO>[];
         for (var item in data) {
      listLoginPAVirtualVO.add(LoginPAVirtualVO.fromJson(item));
  }

        return listLoginPAVirtualVO;
      } else {
        logger.e('login statusCode != 200 $response');

        String messageError =
            data['mensagem'] ?? data['message'] ?? MessageException.general;

        throw ProfileDataException(mensagem: messageError);
      }
    } on NoInternetException catch (error) {
      logger.e('login NoInternetException $error');
      rethrow;
    } on ServiceTimeoutException catch (error) {
      logger.e('login ServiceTimeoutException $error');
      rethrow;
    } on ProfileDataException catch (error) {
      logger.e('login ProfileDataException $error');
      rethrow;
    } on NoDataException catch (error) {
      logger.e('login NoDataException $error');
      rethrow;
    } catch (e) {
      logger.e('login Exception $e');
      throw ProfileDataException(mensagem: MessageException.general);
    }
  }
}
