import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:pavirtual_tablet/bloc/unity/unity_bloc.dart';
import 'package:pavirtual_tablet/bloc/unity/unity_event.dart';
import 'package:pavirtual_tablet/bloc/unity/unity_state.dart';
import 'package:pavirtual_tablet/model/pa_virtual_login.vo.dart';
import 'package:pavirtual_tablet/model/unit_model.dart';
import 'package:pavirtual_tablet/shared/flavor_banner.dart';
//import 'package:header_login/header_login.dart';
import 'package:pavirtual_tablet/shared/flavor_config.dart';
import 'package:pa_virtual/pa_virtual.dart';
import 'package:pavirtual_tablet/bloc/auth/auth_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:pavirtual_tablet/shared/colors.dart';
import 'package:pavirtual_tablet/shared/locator.dart';
import 'package:pavirtual_tablet/shared/utils/pa_virtual_utils.dart';
import 'package:pavirtual_tablet/shared/utils/string_utils.dart';
//import 'package:pavirtual_tablet/shared/twilio_service.dart';
import 'package:pavirtual_tablet/shared/widget/alert.dart';
import 'package:pavirtual_tablet/shared/widget/alert_password.dart';
import 'package:pavirtual_tablet/shared/widget/styles.dart';
import 'package:pa_virtual/utils/locator.dart' as locator_pavirtual;
import 'package:remote_log_elastic/remote_log_elastic.dart';

const paAdult = 'PA_ADULT';
const paChild = 'PA_CHILD';

class SelectCardScreen extends StatefulWidget {
  const SelectCardScreen({Key? key}) : super(key: key);

  @override
  SelectCardScreenState createState() => SelectCardScreenState();
}

class SelectCardScreenState extends State<SelectCardScreen> {
  final TextEditingController cardController = TextEditingController();

  final TextEditingController questionController = TextEditingController();
  TextEditingController tecPasswordAdmin = TextEditingController();

  bool loading = false;
  String nomeBeneficiario = "";
  String unitySelected = "";

final _formKey = GlobalKey<FormState>();
bool _isButtonPressed = false;


  var maskFormatter =  MaskTextInputFormatter(mask: '##/##/####', filter: { "#": RegExp(r'[0-9]') });


  @override
  void initState() {
    super.initState();

    locator_pavirtual.Locator.setup(
      Locator.instance.get<RemoteLog>(),
    );

    cardController.addListener(() {
      setState(() {});
    });

    if (BlocProvider.of<UnityBloc>(context).unitySelected != null) {
      BlocProvider.of<UnityBloc>(context).add(SelectUnityEvent(
          unity: BlocProvider.of<UnityBloc>(context).unitySelected!));
    }
  }

  @override
  void dispose() {
  
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FlavorBanner(
        child: Scaffold(
      appBar: AppBar(
        title: Text("Pa Virtual $unitySelected"),
      ),
      body: Center(
        child: Container(
          padding: const EdgeInsets.all(24.0),
          alignment: Alignment.center,
          child: SingleChildScrollView(
            child: Form(
               key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 24.0),
                    child: BlocListener<UnityBloc, UnityState>(
                      listener: (context, state) {
                        if (state is UnityLoadingState) {
                          setState(() {
                            loading = true;
                          });
                        } else if (state is LoadUnitDone) {
                          modal(context, state.units);
                          setState(() {
                            loading = false;
                          });
                        } else if (state is SelectedUnityState) {
                          setState(() {
                            unitySelected = state.unitySelected.description;
                            loading = false;
                          });
                        } else if (state is ErrorUnitState) {
                          Alert.open(context,
                              title: "Erro ao buscar Unidades",
                              text: state.message);
                          setState(() {
                            loading = false;
                          });
                        } else {
                          setState(() {
                            loading = false;
                          });
                        }
                      },
                      child: GestureDetector(
                        onLongPress: () {
                          _openPasswordAlert();
                          //Locator.instance.get<JitsiService>().test();
                        },
                        onDoubleTap: () {
                          //TwilioService().testRoom(context);
                        },
                        child: Hero(
                          tag: 'logo-unimed',
                          child: Image.asset(
                            'assets/images/logo-unimed.png',
                            width: 250,
                          ),
                        ),
                      ),
                    ),
                  ),
                  TextFormField(
                    controller: cardController,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: 'Carteira/CPF:',
                      hintText: 'Digite uma carteira ou CPF válidos',
                    ),
                    validator: _validateField,
                       onChanged: (value) {
                _formKey.currentState?.validate();
              },
                  inputFormatters: [
                      LengthLimitingTextInputFormatter(16),
                    ],
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(height: 4.0),
                  //_error(),
                  const SizedBox(height: 16.0),
                  _buttonLogin()
                ],
              ),
            ),
          ),
        ),
      ),
    ));
  }


  Widget _buttonLogin() {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthLoading) {
          setState(() {
            loading = true;
          });
        } else if (state is AuthError) {
          setState(() {
            loading = false;
          });
          Alert.open(context,
              title: "Erro ao iniciar PA Virtual", text: state.message);
        } else if (state is AuthAnswerVerified) {
         cardController.text = '';
        Navigator.pushAndRemoveUntil(
  context,
  MaterialPageRoute(
    builder: (context) => FlavorBanner(
      child: PaVirtual(

        nomeBeneficiario: nomeBeneficiario,
        carteira: state.cardLogged,
        isPediatric: paChild == state.redirectTo,
        environment: FlavorConfig.instance!.values.paVirtualEnv,
        remoteLog: Locator.instance.get<RemoteLog>(),
        perfilAppsCredentials: PAVirtualUtils.convertDefaultCredentials(
            FlavorConfig.instance!.values.profilePermissions),
         graphqlCredentials: PAVirtualUtils.convertDefaultCredentials(
          FlavorConfig.instance!.values.graphql),
      ),
    ),
  ),
  (Route<dynamic> route) => route.isFirst,
);
        } else if (state is AuthDone) {
           setState(() {
            
            loading = false;
          });
       
         
          cardController.clear;
          
          if(state.loginPAVirtualVO.length == 1){
            perfilSelected = state.loginPAVirtualVO.first;
          }
          state.loginPAVirtualVO.length > 1 ?
          showDialog(context: context, builder: (context) => _selectUser(perfis:state.loginPAVirtualVO ),).then((value) =>  perfilSelected = null) : confirmationDialog(selectedPerfil: perfilSelected);

           
        } else {
          setState(() {
            loading = false;
          });
        }
      },
      child: loading
          ? const SpinKitThreeBounce(
              color: UnimedColors.green,
              size: 30,
            )
          : ElevatedButton(
              onPressed: _submit,
              child: const Text('Ir para PA Virtual'),
            ),
    );
  }

  
   LoginPAVirtualVO? perfilSelected;

     Widget _selectUser({required List<LoginPAVirtualVO> perfis}) {
    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title:  Text('Selecione a carteira para o atendimento', style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: unimedGreen.shade900,
                      ),),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: perfis.map((perfil) {
              bool isSelected = perfilSelected == perfil;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    perfilSelected = perfil;
                    debugPrint('carteira selecionada ${perfilSelected?.carteira ?? ""}');
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.green.shade100 : null,
                    border: Border.all(
                      color: isSelected ? UnimedColors.green : UnimedColors.greyDisabled,
                    ),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  margin: const EdgeInsets.symmetric(vertical: 5),
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(perfil.nome, style: const TextStyle(fontWeight: FontWeight.bold)),
                      Text(perfil.carteira),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
          actions: [
          
            TextButton(
              onPressed: perfilSelected == null ? null : () {
             
              confirmationDialog(selectedPerfil:perfilSelected );
              },
              style: ButtonStyle(
                foregroundColor: WidgetStateProperty.resolveWith<Color?>(
                  (Set<WidgetState> states) {
                    if (states.contains(WidgetState.disabled)) {
                      return UnimedColors.white;
                    }
                    return UnimedColors.white;
                  },
                ),
                backgroundColor: WidgetStateProperty.resolveWith<Color?>(
                  (Set<WidgetState> states) {
                    if (states.contains(WidgetState.disabled)) {
                      return UnimedColors.greyDisabled;
                    }
                    return UnimedColors.green;
                  },
                ),
              ),
              child: const Text('Confirmar'),
            ),
              ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: UnimedColors.redCancel,
          ),
          child: const Text('Fechar'),
          onPressed: () {
       
              Navigator.of(context).pop();

             
            
          },
        ),
          ],
        );
      },
    );
  }


  void confirmationDialog({required LoginPAVirtualVO? selectedPerfil}){
    return   Alert.open(context,
              title: "Confirmação de dados",
              textWidget: Column(
                children: [_questionInput(selectedPerfil?.pergunta ?? '')],
              ),
              textButtonClose: "Fechar",
              actions: [
                ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: UnimedColors.green,
                    ),
                    child: const Text("Confirmar"),
                    onPressed: () {
                      Navigator.of(context).pop();
                      BlocProvider.of<AuthBloc>(context).add(VerifyAnswerEvent(
                          userAnswer:StringUtils.dateOnlyNumbers(questionController.text)  ,
                          correctAnswer: selectedPerfil?.resposta ?? '' ,
                          redirectTo: selectedPerfil?.redirecionar ?? '', cardlogged: selectedPerfil?.carteira ?? ''));
                      questionController.text = '';
                      perfilSelected = null;
                    }),
              ]);
  }



  Widget _questionInput(String questionText) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 2.0),
          child: Text(
            questionText,
            style: const TextStyle(color: Colors.black, fontSize: 15),
          ),
        ),
        
        TextFormField(
          keyboardType: TextInputType.number,
          controller: questionController,
         validator: (value) {
            if (value != null && value.length < 5) {
              return '';
            }
            return null;
          },
          inputFormatters: cardController.text.length == 16 ? [
            LengthLimitingTextInputFormatter(3),
          ] : [
            maskFormatter
          ],
          decoration: InputDecoration(
            hintText:cardController.text.length == 11 ? "DD/MM/AAAA" : null,
            focusedBorder: StylesConst.customInputBorder(),
            contentPadding: const EdgeInsets.symmetric(horizontal: 8.0),
            border: StylesConst.customInputBorder(),
            errorBorder:
                StylesConst.customInputBorder(color: UnimedColors.redStatus),
          ),
        ),
      ],
    );
  }

  String? loginValidator(String? value) {
    if (value == null) return "";

    if (value.isEmpty) {
      return 'Campo não pode estar vazio';
    } else if (value.replaceAll('.', '')
                    .replaceAll('-', '').trim()
                    .length != 11 && value.replaceAll('.', '')
                    .replaceAll('-', '').trim()
                    .length != 16) {
      return 'CPF deve ter 11 dígitos ou Carteira deve ter 16 dígitos';
    }
    return null;
  }


  String? _validateField(String? value) {
    if (_isButtonPressed && (value == null || value.isEmpty)) {
      return 'Campo não pode estar vazio';
    }
    if (value != null && value.isNotEmpty) {
      final cleanedValue = value.replaceAll('.', '').replaceAll('-', '').trim();
      if (cleanedValue.length != 11 && cleanedValue.length != 16) {
        return 'CPF deve ter 11 dígitos ou Carteira deve ter 16 dígitos';
      }
    }
    return null;
  }

  void _submit() {
    setState(() {
      _isButtonPressed = true;
    });
    if (_formKey.currentState?.validate() ?? false) {
     if (!loading) {
                        BlocProvider.of<AuthBloc>(context)
                            .add(LoginEvent(carteira: cardController.text));
                           
                      }
                    }
    }
  

  Future modal(context, List<UnitModel> units) {
    return showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
          ),
        ),
        builder: (BuildContext context) {
          return Container(
              height: MediaQuery.of(context).size.height * 0.8,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
              child: ListView.builder(
                shrinkWrap: true,
                physics: const ScrollPhysics(),
                itemCount: units.length,
                itemBuilder: (context, i) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 10.0),
                    child: InkWell(
                      onTap: () {
                        BlocProvider.of<UnityBloc>(context)
                            .add(SelectUnityEvent(unity: units[i]));
                        Navigator.pop(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.only(bottom: 20, top: 25),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              width: 2.0,
                              color: UnimedColors.purple,
                            ),
                          ),
                        ),
                        child: Text(
                          units[i].description,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                          softWrap: true,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  );
                },
              ));
        });
  }

  _openPasswordAlert() {
  showDialog(
        context: context,
        builder: (context) {
          return PasswordAvaliationDialog(
              onConfirm: (value) async {
                Navigator.pop(context);

                BlocProvider.of<UnityBloc>(context)
                    .add(GetUnitysEvent(password: tecPasswordAdmin.text));

               
              },
              onClose: () {
                Navigator.pop(context);
                tecPasswordAdmin.clear();
              },
              tecPassword: tecPasswordAdmin);
        });
  }

  
}
