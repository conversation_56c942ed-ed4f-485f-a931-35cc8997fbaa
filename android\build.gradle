buildscript {
    ext.kotlin_version = '2.0.21'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.7.2'
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}


subprojects {
   afterEvaluate { project ->
       if (project.hasProperty('android')) {
           project.android {
               if (namespace == null) {
                   namespace project.group
               }
           }
       }
   }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
