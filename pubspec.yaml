name: pavirtual_tablet
description: "A new Flutter project."
publish_to: 'none' 

version: 2.0.1+201003

environment:
  sdk: ">=3.0.5 <4.0.0"
  flutter: "3.32.5"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.2
  intl: ^0.20.2
  http: ^1.1.0
  logger: ^2.0.2+1
  flutter_i18n: ^0.36.3
  package_info_plus: ^8.3.0
  json_annotation: ^4.4.0
  auto_size_text: ^3.0.0
  get_it: ^7.2.0
  after_layout: ^1.1.0
  native_device_orientation: ^1.0.0
  flutter_spinkit: ^5.1.0
  mask_text_input_formatter: ^2.0.0
  collection: ^1.16.0
  path_provider: ^2.0.7
  graphql: ^5.1.3
  connectivity_plus: ^6.1.4

  firebase_crashlytics: ^3.0.4
  firebase_core: ^2.1.1

  # bloc packages
  flutter_bloc: ^7.3.3
  equatable: ^2.0.3
  hydrated_bloc: ^7.1.0

  remote_log_elastic:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: remote_log_elastic
      ref: remote_log_elastic-v4.0.0

  splash_unimed:
    path: C:\Users\<USER>\OneDrive\Documentos\unimed\flutter_packages\splash_unimed
    # git:
    #   url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
    #   path: splash_unimed
    #   ref: splash_unimed-v2.0.1

  evaluation:
    path: C:\Users\<USER>\OneDrive\Documentos\unimed\flutter_packages\evaluation
    # git:
    #   url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
    #   path: evaluation
    #   ref: evalution-v5.0.1

  header_login:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: header_login
      ref: header_login-v1.0.6

  pa_virtual:
    path: C:\Users\<USER>\OneDrive\Documentos\unimed\flutter_packages\pa_virtual
    # git:
    #   url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
    #   path: pa_virtual
    #   ref: pa_virtual-v7.1.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/pin_maps/
    - assets/i18n/
    - assets/images/feature_discovery/ve_service/
    - assets/animations/

  fonts:
    - family: UnimedSans
      fonts:
        - asset: assets/fonts/UnimedSans-Regular.otf
        - asset: assets/fonts/UnimedSans-RegularItalic.otf
          style: italic
    - family: UnimedSlab
      fonts:
        - asset: assets/fonts/unimed-slab/UnimedSlab-Regular.otf
    - family: UnimedIcons
      fonts:
        - asset: assets/fonts/UnimedIcons.ttf
    - family: uicons
      fonts:
        - asset: assets/icomoon/fonts/uicons.ttf
    - family: ConectaSaudeIcons
      fonts:
        - asset: assets/fonts/conecta-saude/ConectaSaudeIcons.ttf