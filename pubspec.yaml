name: pavirtual_tablet
description: A new Flu
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 2.0.1+201003

environment:
  sdk: ">=2.17.6 <4.0.0"
  flutter: ">=3.24.5"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.8
  intl: ^0.18.1
  http: ^1.4.0
  logger: ^2.6.0
  flutter_i18n: ^0.34.0
  package_info_plus: ^8.3.0
  json_annotation: ^4.9.0
  auto_size_text: ^3.0.0
  get_it: ^8.0.3
  after_layout: ^1.2.0
  native_device_orientation: ^1.0.0
  flutter_spinkit: ^5.2.1
  mask_text_input_formatter: ^2.9.0
  collection: ^1.19.1
  path_provider: ^2.1.5
  connectivity_plus: ^6.1.4

  firebase_crashlytics: ^4.3.7
  firebase_core: ^3.14.0

  # bloc packages
  flutter_bloc: ^7.3.3
  equatable: ^2.0.7
  hydrated_bloc: ^10.0.0

  remote_log_elastic:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: remote_log_elastic
      ref: remote_log_elastic-v4.0.0

  splash_unimed:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: splash_unimed
      ref: splash_unimed-v2.0.0

  evaluation:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: evaluation
      ref: evalution-v5.0.0

  header_login:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: header_login
      ref: header_login-v1.0.6

  pa_virtual:
    git:
      url: ssh://*********************************:2222/novas-tecnologias/flutter_packages.git
      path: pa_virtual
      ref: pa_virtual-v7.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0

dependency_overrides:
  win32: ^5.14.0
  native_device_orientation: ^1.0.0
  intl: ^0.18.1
  flutter_i18n: ^0.34.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/pin_maps/
    - assets/i18n/
    - assets/images/feature_discovery/ve_service/
    - assets/animations/

  fonts:
    - family: UnimedSans
      fonts:
        - asset: assets/fonts/UnimedSans-Regular.otf
        - asset: assets/fonts/UnimedSans-RegularItalic.otf
          style: italic
    - family: UnimedSlab
      fonts:
        - asset: assets/fonts/unimed-slab/UnimedSlab-Regular.otf
    - family: UnimedIcons
      fonts:
        - asset: assets/fonts/UnimedIcons.ttf
    - family: uicons
      fonts:
        - asset: assets/icomoon/fonts/uicons.ttf
    - family: ConectaSaudeIcons
      fonts:
        - asset: assets/fonts/conecta-saude/ConectaSaudeIcons.ttf
