import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_i18n/flutter_i18n_delegate.dart';
import 'package:flutter_i18n/loaders/e2e_file_translation_loader.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:pavirtual_tablet/blocs_load.dart';
import 'package:pavirtual_tablet/screen/select_card_screen.dart';
import 'package:pavirtual_tablet/shared/colors.dart';
import 'package:pa_virtual/blocs_load.dart' as pavirtual_blocloads;
import 'package:pavirtual_tablet/shared/flavor_banner.dart';
import 'package:splash_unimed/splash_unimed.dart';

class PaTablet extends StatelessWidget {
  const PaTablet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    return BlocsLoad(
        child: pavirtual_blocloads.BlocsLoad(
            child: MaterialApp(
      home: FlavorBanner(
          child: PopScope(
        canPop: false, // allowHardwareBack flag to enable/disable
        child: SplashPage(next: (context) => const SelectCardScreen()),
      )),
      theme: ThemeUnimed.green(),
      localizationsDelegates: [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        DefaultCupertinoLocalizations.delegate,
        FlutterI18nDelegate(
          translationLoader: E2EFileTranslationLoader(
            useCountryCode: true,
            basePath: 'assets/i18n',
            fallbackFile: 'pt_BR',
            forcedLocale: const Locale('pt', 'BR'),
          ),
        ),
      ],
    )));
  }
}
