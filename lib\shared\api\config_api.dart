import 'dart:async';
import 'dart:convert';
import 'package:pa_virtual/shared/api/auth.api.dart';
import 'package:pavirtual_tablet/model/unit_model.dart';
import 'package:pavirtual_tablet/shared/flavor_config.dart';
import 'package:pa_virtual/shared/exceptions.dart';
import 'package:pavirtual_tablet/shared/http_client.dart';
import 'package:pavirtual_tablet/shared/locator.dart';
import 'package:pavirtual_tablet/shared/logger_print.dart';
import 'package:pavirtual_tablet/shared/utils/messages.exceptions.dart';

class ConfigApi {
  final UnimedHttpClient httpClient;

  final logger = UnimedLogger(className: 'ConfigApi');

  ConfigApi(
    this.httpClient,
  );

  Future<List<UnitModel>> getUnits({required String password}) async {
    try {
      final url =
          '${FlavorConfig.instance!.values.profilePermissions.url}pa/app/units';

      final token = await Locator.instance.get<AuthApi>().tokenPerfilApps();

      final headers = {
        'Authorization': "Bearer $token",
        'Content-Type': 'application/json',
      };

      final body = jsonEncode({"password": password});

      final response =
          await httpClient.post(Uri.parse(url), headers: headers, body: body);

      final data = jsonDecode(response.body);
      if (response.statusCode == 200) {
        final collection = (data as List)
            .map<UnitModel>((e) => UnitModel.fromJson(e))
            .toList();

        logger.d('getUnits success units length => ${collection.length}');
        if (collection.isEmpty) {
          throw NoDataException();
        }

        return collection;
      } else {
        logger.e('getUnits statusCode != 200 $response');

        String messageError =
            data['mensagem'] ?? data['message'] ?? MessageException.general;

        throw UnimedException(messageError);
      }
    } on NoInternetException catch (error) {
      logger.e('getUnits NoInternetException $error');
      rethrow;
    } on ServiceTimeoutException catch (error) {
      logger.e('getUnits ServiceTimeoutException $error');
      rethrow;
    } on NoDataException catch (error) {
      logger.e('getUnits NoDataException $error');
      rethrow;
    } on UnimedException catch (error) {
      logger.e('getUnits UnimedException $error');
      rethrow;
    } catch (e) {
      logger.e('getUnits Exception $e');
      throw ProfileDataException(mensagem: MessageException.general);
    }
  }
}
