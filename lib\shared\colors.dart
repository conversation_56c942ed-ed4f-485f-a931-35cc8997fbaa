import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pa_virtual/shared/fonts_const.dart';

const MaterialColor unimedGreen = MaterialColor(
  0xFF00995D,
  <int, Color>{
    50: Color(0xFFe0f3ec),
    100: Color(0xFFb3e0ce),
    200: Color(0xFF80ccae),
    300: Color(0xFF4db88e),
    400: Color(0xFF26a875),
    500: Color(0xFF00995d),
    600: Color(0xFF009155),
    700: Color(0xFF00864b),
    800: Color(0xFF007c41),
    900: Color(0xFF006b30),
  },
);

const MaterialColor unimedOrange = MaterialColor(
  0xFFF47920,
  <int, Color>{
    50: Color(0xFFfeefe4),
    100: Color(0xFFfcd7bc),
    200: Color(0xFFfabc90),
    300: Color(0xFFf7a163),
    400: Color(0xFFf68d41),
    500: Color(0xFFf47920),
    600: Color(0xFFf3711c),
    700: Color(0xFFf16618),
    800: Color(0xFFef5c13),
    900: Color(0xFFec490b),
  },
);

const MaterialColor unimedRed = MaterialColor(
  0xFFCA203C,
  <int, Color>{
    50: Color(0xFFF9E4E8),
    100: Color(0xFFEFBCC5),
    200: Color(0xFFE5909E),
    300: Color(0xFFDA6377),
    400: Color(0xFFD24159),
    500: Color(0xFFCA203C),
    600: Color(0xFFC51C36),
    700: Color(0xFFBD182E),
    800: Color(0xFFB71327),
    900: Color(0xFFAB0B1A),
  },
);

class UnimedColors {
  static const Color greyCard = Color.fromRGBO(117, 120, 123, 1);
  static const Color greyBackground = Color(0xFFefefef);
  static const Color greyDisabled = Color.fromRGBO(196, 203, 207, 1);
  static const Color gray = Color.fromRGBO(244, 244, 244, 1);
  static const Color grayLight = Color.fromRGBO(239, 239, 239, 1);
  static const Color grayLight2 = Color.fromRGBO(193, 193, 193, 1);
  static const Color grayLight3 = Color.fromRGBO(196, 203, 207, 1);
  static const Color grayDark = Color.fromRGBO(112, 112, 112, 1);
  static const Color grayDark2 = Color.fromRGBO(77, 77, 77, 1);
  static const Color green = Color.fromRGBO(0, 153, 93, 1);
  static const Color greenLight = Color.fromRGBO(190, 215, 0, 1);
  static const Color greenLight2 = Color.fromRGBO(163, 212, 58, 0.3);
  static const Color greenDark = Color.fromRGBO(10, 95, 85, 1);
  static const Color orange = Color.fromRGBO(244, 121, 32, 1);
  static const Color orange2 = Color.fromRGBO(255, 150, 75, 1);

  static const Color purple = Color.fromRGBO(65, 21, 100, 1);
  static const Color yellowLight = Color.fromRGBO(218, 228, 142, 1);
  static const Color greenAsccentChart = Color.fromRGBO(190, 215, 0, 1);
  static const Color pinkChart = Color.fromRGBO(237, 22, 81, 1);
  static const Color greenChart = Color.fromRGBO(0, 133, 81, 1);

  static const Color unimedFone = Color.fromRGBO(104, 45, 0, 1);
  static const Color unimedAeroMedico = Color.fromRGBO(8, 95, 95, 1);
  static const Color opcionalGray2 = Color.fromRGBO(138, 138, 138, 1);
  static const Color opcionalGray = Color.fromRGBO(233, 233, 233, 1);

  static const Color buttonGreenDisabled = Color.fromRGBO(126, 203, 173, 1);
  static const Color buttonGreen = Color.fromRGBO(0, 153, 93, 1);

  static const Color purpleStatus = Color.fromRGBO(163, 35, 142, 1);
  static const Color blueStatus = Color.fromRGBO(65, 21, 100, 1);
  static const Color redStatus = Color.fromRGBO(204, 68, 75, 1);

  static const Color discoveryOverlay = Color.fromRGBO(0, 0, 0, .75);
  static const Color purpleVirtualCard = Color(0xFFA3238E);

  //Unimed Conecta Colors
  static const Color conectaOrange = Color(0xFFF47920);
  static const Color conectaGreen = Color(0xFF00995D);
  static const Color conectaViolet = Color(0xFFA3238E);
  static const Color conectaPurple = purple;
  static const Color conectaPink = Color(0xFFED1651);
  static const Color white = Color(0xFFFFFFFF);
  static const Color darkColor = Color(0xFF401962);
  static const Color grey = Color(0xFF444444);
  static const Color lightGrey = Color(0xFFC4CBCF);
  static const Color redCancel = Color(0xFFCA203C);

  //Evaluation Colors
  static const Color darkRed = Color.fromRGBO(151, 41, 49, 1);
  static const Color darkPink = Color.fromRGBO(202, 32, 60, 1);
  static const Color darkOrange = Color.fromRGBO(196, 97, 26, 1);
  static const Color yellow = Color.fromRGBO(255, 203, 11, 1);
  static const Color greenLight3 = Color.fromRGBO(208, 229, 147, 1);
  static const Color greenLight4 = Color.fromRGBO(177, 211, 75, 1);
  static const Color greenLight5 = Color.fromRGBO(1, 134, 82, 1);
}

class ThemeUnimed {
  static ThemeData green() {
    return ThemeData(
      appBarTheme: const AppBarTheme(
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      primarySwatch: unimedGreen,
      buttonTheme: const ButtonThemeData(
        minWidth: 10,
        buttonColor: unimedGreen,
        textTheme: ButtonTextTheme.primary,
      ),
      fontFamily: FontsConst.unimedSans,
      textTheme: const TextTheme(
          bodyMedium: TextStyle(
            color: UnimedColors.grayDark2,
          ),
          titleLarge: TextStyle(
            color: UnimedColors.green,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          )),
    );
  }
}
