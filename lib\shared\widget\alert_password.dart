import 'package:flutter/material.dart';
import 'package:pavirtual_tablet/shared/colors.dart';

class PasswordAvaliationDialog extends StatelessWidget {
  PasswordAvaliationDialog(
      {super.key,
      required this.onClose,
      this.textButton = "Ok",
      this.textClose = "Fechar",
      required this.tecPassword,
      this.colorIcon = UnimedColors.greenDark,
      this.colorButton = UnimedColors.orange,
      required this.onConfirm});

  final VoidCallback onClose;
  final String textButton;
  final String textClose;
  final Color colorIcon;
  final Color colorButton;
  final Function onConfirm;
  final TextEditingController tecPassword;

  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title: const Text("Insira a senha de configuração"),
      content: SingleChildScrollView(
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              const Divider(
                height: 5.0,
                color: Colors.transparent,
              ),
              const SizedBox(height: 10.0),
              Form(
                key: _formKey,
                child: TextFormField(
                  controller: tecPassword,
                  obscureText: true,
                  textInputAction: TextInputAction.done,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'Insira uma senha';
                    }

                    return null;
                  },
                  decoration: const InputDecoration(
                    prefixIcon: Icon(
                      Icons.lock,
                      color: unimedGreen,
                    ),
                    hintText: 'Senha',
                    labelText: 'Senha',
                  ),
                  style: const TextStyle(color: UnimedColors.green),
                ),
              ),
            ]),
      ),
      backgroundColor: Colors.white,
      actions: [
        ElevatedButton(
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.all(10.0),
              backgroundColor: unimedOrange,
              textStyle: const TextStyle(color: Colors.white),
            ),
            onPressed: onClose,
            child: Text(textClose)),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: UnimedColors.green,
          ),
          onPressed: () => _submit(context),
          child: const Text("Confirmar"),
        ),
      ],
    );
  }

  Future<void> _submit(context) async {
    if (_formKey.currentState!.validate()) {
      await onConfirm(tecPassword.text);
      tecPassword.clear();
    }
  }
}
