import 'package:equatable/equatable.dart';
import 'package:pavirtual_tablet/model/unit_model.dart';

abstract class UnityState extends Equatable {
  const UnityState();

  @override
  List<Object> get props => [];
}

class UnityInitialState extends UnityState {}

class UnityLoadingState extends UnityState {}

class LoadUnitDone extends UnityState {
  const LoadUnitDone({
    required this.units,
  });
  final List<UnitModel> units;
}

class ErrorUnitState extends UnityState {
  const ErrorUnitState({required this.message});
  final String message;
  @override
  List<Object> get props => [message];
}

class NoDataUnityState extends UnityState {}

class SelectedUnityState extends UnityState {
  final UnitModel unitySelected;

  @override
  List<Object> get props => [unitySelected];

  const SelectedUnityState({required this.unitySelected});
}
