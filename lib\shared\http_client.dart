import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:http/http.dart' as http;
import 'package:pavirtual_tablet/shared/exceptions.dart';
import 'package:pavirtual_tablet/shared/locator.dart';
import 'package:pavirtual_tablet/shared/logger_print.dart';
import 'package:pavirtual_tablet/shared/utils/http.dart';
import 'package:pavirtual_tablet/shared/version.service.dart';

//const _DEFAULT_TIMEOUT = Duration(seconds: 30);
const limitCharacters = 2000;

class UnimedHttpClient extends http.BaseClient {
  final UnimedLogger logger = UnimedLogger(className: 'UnimedHttpClient');

  final Map<String, String>? defaultHeaders;

  String? _versionNumber;

  Duration _defaultTimeout = const Duration(seconds: 30);
  Duration _customTimeout = const Duration(seconds: 30);

  Duration get timeout => _customTimeout;
  //Duration get defaultTimeout => _defaultTimeout;

  /// It will be assigned only if the timeout is greater than 0
  ///
  /// The value will only be assigned per request, then it will be changed to the
  /// default value
  set timeout(Duration timeout) => {
        if (timeout.inSeconds > 0) {_customTimeout = timeout}
      };

  UnimedHttpClient({this.defaultHeaders});

  void setToDefaultTimeout(int? timeout) {
    if (timeout != null && timeout > 0) {
      logger.d('set default timeout after login: $timeout');
      this.timeout = Duration(seconds: timeout);
      _defaultTimeout = Duration(seconds: timeout);
    }
  }

  void _backToDefaultTimeout() {
    timeout = _defaultTimeout;
  }

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    final Stopwatch stopwatch = Stopwatch()..start();

    request.headers.addAll(defaultHeaders!);

    logger.d('send headers : ${request.headers}');
    final httpClient = HttpUtils.bypassInvalidCertificate();

    try {
      final tmpTimeout = timeout;

      _backToDefaultTimeout();

      final response = await httpClient.send(request).timeout(tmpTimeout);
      logger.d(
          'SEND url: ${request.url}      -     time: ${stopwatch.elapsed.inMilliseconds} (${response.statusCode})');
      stopwatch.stop();
      return response;
    } on SocketException catch (ex) {
      logger.e('httpClient send SocketException $ex');
      throw NoInternetException();
    } on HandshakeException catch (ex) {
      logger.e('httpClient send HandshakeException $ex');
      throw NoInternetException();
    } on Exception catch (ex) {
      logger.e('httpClient send Exception $ex');
      throw UnimedException(ex.toString());
    } finally {
      httpClient.close();
    }
  }

  @override
  Future<http.Response> get(url, {Map<String, String>? headers}) async {
    final Stopwatch stopwatch = Stopwatch()..start();
    final header = await _handleHeaders(headers);

    logger.d('GET url     : $url');
    logger.d('GET headers : $header');

    final tmpTimeout = timeout;

    final httpClient = HttpUtils.bypassInvalidCertificate();

    _backToDefaultTimeout();

    try {
      final response = await httpClient
          .get(url, headers: header)
          .timeout(tmpTimeout, onTimeout: () {
        throw ServiceTimeoutException();
      });

      logger.d(
          'GET url: $url     -     time: ${stopwatch.elapsed.inMilliseconds} (${response.statusCode})');

      stopwatch.stop();

      return response;
    } on ServiceTimeoutException {
      rethrow;
    } on SocketException catch (ex) {
      logger.e('httpClient get SocketException $ex');
      throw NoInternetException();
    } on HandshakeException catch (ex) {
      logger.e('httpClient get HandshakeException $ex');
      throw NoInternetException();
    } on Exception catch (ex) {
      logger.e('httpClient get Exception $ex');
      throw UnimedException(ex.toString());
    } finally {
      httpClient.close();
    }
  }

  @override
  Future<http.Response> post(
    url, {
    Map<String, String>? headers,
    body,
    Encoding? encoding,
    bool logBody = true,
  }) async {
    final Stopwatch stopwatch = Stopwatch()..start();
    final header = await _handleHeaders(headers);

    logger.d('POST url     : $url');
    logger.d('POST headers : $header');

    if (logBody) {
      logger.d(
          'POST body    : ${(body ?? "").toString().characters.take(limitCharacters)}');
    }

    // https://dart.dev/guides/language/language-tour#assignment-operators
    encoding ??= Encoding.getByName('utf-8');

    final tmpTimeout = timeout;

    final httpClient = HttpUtils.bypassInvalidCertificate();

    _backToDefaultTimeout();

    try {
      final response = await httpClient
          .post(
        url,
        body: body,
        headers: header,
        encoding: encoding,
      )
          .timeout(
        tmpTimeout,
        onTimeout: () {
          throw ServiceTimeoutException();
        },
      );
      logger.d(
          'POST url: $url     -       time: ${stopwatch.elapsed.inMilliseconds} (${response.statusCode})');
      stopwatch.stop();
      return response;
    } on SocketException catch (ex) {
      logger.e('httpClient post SocketException $ex');
      throw NoInternetException();
    } on HandshakeException catch (ex) {
      logger.e('httpClient post HandshakeException $ex');
      throw NoInternetException();
    } on Exception catch (ex) {
      logger.e('httpClient post Exception code ${ex.toString()}');
      throw UnimedException(ex.toString());
    } finally {
      httpClient.close();
    }
  }

  @override
  Future<http.Response> put(url,
      {Map<String, String>? headers, body, Encoding? encoding}) async {
    final Stopwatch stopwatch = Stopwatch()..start();

    final header = await _handleHeaders(headers);

    logger.d('PUT headers : $header');
    logger.d('PUT body    : $body');

    // https://dart.dev/guides/language/language-tour#assignment-operators
    encoding ??= Encoding.getByName('utf-8');

    final tmpTimeout = timeout;

    final httpClient = HttpUtils.bypassInvalidCertificate();

    logger.d(
        'POST body    : ${(body ?? "").toString().characters.take(limitCharacters)}');

    _backToDefaultTimeout();

    try {
      final response = await httpClient
          .put(
        url,
        body: body,
        headers: header,
        encoding: encoding,
      )
          .timeout(tmpTimeout, onTimeout: () {
        throw ServiceTimeoutException();
      });
      logger.d(
          'PUT url: $url       -       ${stopwatch.elapsed.inMilliseconds} (${response.statusCode})');
      stopwatch.stop();
      return response;
    } on SocketException catch (ex) {
      logger.e('httpClient put SocketException $ex');
      throw NoInternetException();
    } on HandshakeException catch (ex) {
      logger.e('httpClient put HandshakeException $ex');
      throw NoInternetException();
    } on Exception catch (ex) {
      logger.e('httpClient put Exception code ${ex.toString()}');
      throw UnimedException(ex.toString());
    } finally {
      httpClient.close();
    }
  }

  @override
  Future<http.Response> delete(url,
      {Map<String, String>? headers, body, Encoding? encoding}) async {
    final Stopwatch stopwatch = Stopwatch()..start();
    final header = await _handleHeaders(headers);

    logger.d('DELETE url     : $url');
    logger.d('DELETE headers : $header');

    final tmpTimeout = timeout;

    final httpClient = HttpUtils.bypassInvalidCertificate();

    _backToDefaultTimeout();

    try {
      final response = await httpClient
          .delete(
        url,
        body: body,
        headers: header,
        encoding: encoding,
      )
          .timeout(tmpTimeout, onTimeout: () {
        throw ServiceTimeoutException();
      });

      logger.d(
          'DELETE url: $url     -     time: ${stopwatch.elapsed.inMilliseconds} (${response.statusCode})');

      stopwatch.stop();

      return response;
    } on TimeoutException {
      rethrow;
    } on SocketException catch (ex) {
      logger.e('httpClient delete SocketException $ex');
      throw NoInternetException();
    } on HandshakeException catch (ex) {
      logger.e('httpClient delete HandshakeException $ex');
      throw NoInternetException();
    } on Exception catch (ex) {
      logger.e('httpClient delete Exception $ex');
      throw UnimedException(ex.toString());
    } finally {
      httpClient.close();
    }
  }

  Future<Map<String, String>> _handleHeaders(
      Map<String, String>? headers) async {
    final headersReturn = <String, String>{};

    if (defaultHeaders != null) headersReturn.addAll(defaultHeaders!);
    if (headers != null) headersReturn.addAll(headers);
    if (_versionNumber == null) {
      await _setVersionHeader();
    }

    headersReturn.addAll({"appclienteversion": _versionNumber ?? ""});

    return headersReturn;
  }

  _setVersionHeader() async {
    final version = await (Locator.instance.get<VersionService>().getInfo());
    _versionNumber = version.version;
  }
}
