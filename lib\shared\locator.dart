import 'package:evaluation/evaluation.dart';
import 'package:pavirtual_tablet/shared/api/auth_api.dart';
import 'package:pavirtual_tablet/shared/api/config_api.dart';
import 'package:pavirtual_tablet/shared/flavor_config.dart';
import 'package:pavirtual_tablet/shared/http_client.dart';
import 'package:pavirtual_tablet/shared/version.service.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';
import 'package:get_it/get_it.dart';

class Locator {
  static late GetIt _i;
  static GetIt get instance => _i;

  Locator.setup() {
    _i = GetIt.I;

    _i.registerSingleton<RemoteLog>(
      RemoteLog(
          environment: FlavorConfig.instance!.values.remoteLogEnv,
          username: 'pa-tablet',
          password: 'BwQYzP3eIhs4SEwMouhsijRZ90Uh6G',
          disableUpload: false,
          // disableUpload: FlavorConfig.instance.values.remoteLogEnv !=
          //     RemoteLogEnv.PROD, // Enable remote_log only production
          showLogs: false),
    );

    _i.registerSingleton<UnimedHttpClient>(
      UnimedHttpClient(defaultHeaders: {"Content-Type": "application/json"}),
    );

    _i.registerSingleton<VersionService>(VersionService());

    _i.registerLazySingleton<AuthApi>(
        () => AuthApi(_i.get<UnimedHttpClient>()));

    _i.registerLazySingleton<ConfigApi>(
        () => ConfigApi(_i.get<UnimedHttpClient>()));

    _i.registerSingleton<Evaluation>(
      Evaluation(
          environment: FlavorConfig.instance!.values.evaluationEnv,
          username: 'pa-tablet',
          password: 'BwQYzP3eIhs4SEwMouhsijRZ90Uh6G',
          disableUpload: false,
          // disableUpload: FlavorConfig.instance.values.remoteLogEnv !=
          //     RemoteLogEnv.PROD, // Enable remote_log only production
          showLogs: false),
    );
  }

  Locator.testSetup() {
    _i = GetIt.I;

    _i.registerSingleton<RemoteLog>(
      RemoteLog(
          environment: FlavorConfig.instance!.values.remoteLogEnv,
          username: 'cliente-app',
          password: 'FmR6fqSfQLx8Eyea',
          disableUpload: false,
          // disableUpload: FlavorConfig.instance.values.remoteLogEnv !=
          //     RemoteLogEnv.PROD, // Enable remote_log only production
          showLogs: false),
    );

    _i.registerSingleton<UnimedHttpClient>(
      UnimedHttpClient(defaultHeaders: {"Content-Type": "application/json"}),
    );
  }
}
