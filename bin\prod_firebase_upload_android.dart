// ignore_for_file: avoid_print

import 'dart:convert';
import 'dart:io';
import 'package:collection/collection.dart' show IterableExtension;

//import 'package:http/io_client.dart';

String? _buildNumber;
const discordWebhook =
    "https://discord.com/api/webhooks/777861085168402522/SJe70peWILrizi8GFz32Jq99Adq4hLW0m4ooT_g6zfIWAXI6mUSI8z9l5JTBKDEL98wh";
const appInAndroid = '1:1087143606719:android:90e710c3c4064c7f87490a';

// IOClient _bypassInvalidCertificate() {
//   final ioc = new HttpClient();
//   ioc.badCertificateCallback =
//       (X509Certificate cert, String host, int port) => true;
//   return IOClient(ioc);
// }

Future<String?> _getBuildNumber() async {
  _buildNumber = null;

  List<String> lines = await File('./android/local.properties').readAsLines();

  String? lineSelected;

  lineSelected = lines
      .firstWhereOrNull((element) => element.contains('flutter.versionCode'));

  if (lineSelected != null) {
    _buildNumber = lineSelected.split("=")[1];
  }

  return _buildNumber;
}

void _sendDiscordMessage(String message) async {
  // final httpClient = _bypassInvalidCertificate();

  // try {
  //   final response = await httpClient.post(
  //     Uri.parse(discordWebhook),
  //     body: jsonEncode({"content": message}),
  //     headers: {"Content-Type": "application/json"},
  //     encoding: Encoding.getByName('utf-8'),
  //   );
  //   print('discord send status ${response.statusCode}');
  // } catch (ex) {
  //   print('Error send discord message $ex');
  // }
}

void _uploadFirebase() {
  final parameters = [
    'appdistribution:distribute',
    'build/app/outputs/apk/release/app-release.apk',
    '--app',
    appInAndroid,
    '--release-notes',
    'PROD Autodeploy',
    '--testers',
    '<EMAIL>'
  ];
  // firebase appdistribution:distribute build/app/outputs/apk/release/app-release.apk --app 1:1031315899788:android:48edd931a685a61c --release-notes "Versão deploy by firebase cli" --groups "usuarios"
  Process.start('firebase', parameters).then((Process process) {
    process.exitCode.then((exitCode) {
      print('exit code: $exitCode');

      final message =
          "PROD PA Tablet - build $_buildNumber\n\nEnviado com sucesso.";

      _sendDiscordMessage(message);
    });

    process.stderr.transform(utf8.decoder).listen((data) {
      print(data);
    });

    process.stdout.transform(utf8.decoder).listen((data) {
      print(data);
    });
  }).catchError((onError) {
    print('onError $onError');
  });
}

void _buildApp(List<String> args) {
  const message = "PROD Cliente Minha Unimed\nBuildando o projeto...";

  _sendDiscordMessage(message);

  Process.start('flutter', [
    'build',
    'apk',
    '-t',
    'lib/main-prod.dart',
    '--verbose',
    '--release',
    ...args,
  ]).then((Process process) {
    process.exitCode.then((exitCode) {
      print('exit code: $exitCode');

      _getBuildNumber().then((buildNumber) {
        if (buildNumber != null) {
          print('Criando versão $buildNumber');
          final message =
              "PROD Cliente Minha Unimed - build $buildNumber\n\nEnviando para Firebase...";
          _sendDiscordMessage(message);
        }

        _uploadFirebase();
      }).catchError((onError) {
        print('Error get buildNumber $onError');
      });
    });
    process.stdout.transform(utf8.decoder).listen((data) {
      print(data);
    });

    process.stderr.transform(utf8.decoder).listen((data) {
      print(data);
    });
  }).catchError((onError) {
    print('onError $onError');
  });
}

void main(List<String> args) {
  _buildApp(args);
}
