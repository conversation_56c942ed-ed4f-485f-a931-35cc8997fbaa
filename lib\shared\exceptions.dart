class UnimedException {
  final String message;

  UnimedException(this.message);

  @override
  String toString() {
    return message;
  }
}

class ServiceTimeoutException extends UnimedException {
  ServiceTimeoutException()
      : super(
            'Não foi possível atender sua solicitação no momento. Tente novamente mais tarde. (TIMEOUT)');
}

class NoInternetException extends UnimedException {
  NoInternetException() : super('Você está sem internet no momento.');
}

class AgendamentoException extends UnimedException {
  AgendamentoException(String message) : super(message);
}

class AuthException extends UnimedException {
  AuthException(String message) : super(message);
}

class OpenSessionAutorizacoesException extends UnimedException {
  OpenSessionAutorizacoesException(String message) : super(message);
}

class AutorizacoesException extends UnimedException {
  AutorizacoesException(String message) : super(message);
}

class BeneficiarioException extends UnimedException {
  BeneficiarioException(String message) : super(message);
}

class BeneficiarioWarningException extends UnimedException {
  BeneficiarioWarningException(String message) : super(message);
}

class CoparticipacaoNoDataException extends UnimedException {
  CoparticipacaoNoDataException(String message) : super(message);
}

class FinanceiroException extends UnimedException {
  FinanceiroException(String message) : super(message);
}

class FinanceiroWarningException extends UnimedException {
  FinanceiroWarningException(String message) : super(message);
}

class GuiaMedicoException extends UnimedException {
  GuiaMedicoException(String message) : super(message);
}

class OpcionaisException extends UnimedException {
  OpcionaisException(String message) : super(message);
}

class ProfilesException extends UnimedException {
  ProfilesException(String message) : super(message);
}

class PortalException extends UnimedException {
  PortalException(String message) : super(message);
}

class SignupException extends UnimedException {
  SignupException(String message) : super(message);
}

class GuideException extends UnimedException {
  GuideException(String message) : super(message);
}

class SendTokenException extends UnimedException {
  SendTokenException(String message) : super(message);
}

class TeleconsultaException extends UnimedException {
  TeleconsultaException(String message) : super(message);
}

class ConsultaException extends UnimedException {
  ConsultaException(String message) : super(message);
}

class VirtualCardException extends UnimedException {
  VirtualCardException(message) : super(message);
}

class VirtualPAVerificationException extends UnimedException {
  VirtualPAVerificationException(String message) : super(message);
}

class VirtualEmergencyException extends UnimedException {
  VirtualEmergencyException(String message) : super(message);
}

class VirtualEmergencyStatusRoomException extends UnimedException {
  VirtualEmergencyStatusRoomException()
      : super('Não foi possivel consultar status do atendimento');
}

class PdfException extends UnimedException {
  PdfException(String message) : super(message);
}

class ProvidersException extends UnimedException {
  ProvidersException(String message) : super(message);
}

class GeneralConfigException extends UnimedException {
  GeneralConfigException(String message) : super(message);
}

class GetNetException extends UnimedException {
  final int code;

  GetNetException(this.code, message) : super(message);
}

class GenericException extends UnimedException {
  GenericException()
      : super(
            'Não foi possível atender sua solicitação no momento. Tente novamente mais tarde.');
}

class NoDataException extends UnimedException {
  NoDataException() : super("Não existem dados no momento.");
}

class QueueException extends UnimedException {
  QueueException(String message) : super(message);
}

class WaitingTimeException extends UnimedException {
  WaitingTimeException(String message) : super(message);
}

class PrivacyPolicyException extends UnimedException {
  PrivacyPolicyException(String message) : super(message);
}

class CheckinLabException extends UnimedException {
  CheckinLabException(String message) : super(message);
}

class CheckinSurgeryException extends UnimedException {
  CheckinSurgeryException(String message) : super(message);
}
