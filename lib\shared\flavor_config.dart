import 'package:evaluation/evaluation.dart';
import 'package:pa_virtual/pa_virtual.dart';
import 'package:pavirtual_tablet/shared/utils/string_utils.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

enum Flavor { dev, test, production }

class FlavorValues {
  FlavorValues({
    // @required this.portal,
    required this.remoteLogEnv,
    required this.profilePermissions,
    required this.evaluationEnv,
    required this.paVirtualEnv,
    required this.graphql,
  });

  final RemoteLogEnv remoteLogEnv;
  final DefaultCredencial profilePermissions;
  // final Portal portal;
  final EvaluationEnv evaluationEnv;
  final PAVirtualEnv paVirtualEnv;
  DefaultCredencial graphql;
}

class FlavorConfig {
  final Flavor flavor;
  final String name;

  final FlavorValues values;
  static FlavorConfig? _instance;

  factory FlavorConfig({required Flavor flavor, required FlavorValues values}) {
    _instance ??= FlavorConfig._internal(
        flavor, StringUtils.enumName(flavor.toString()), values);
    return _instance!;
  }

  FlavorConfig._internal(this.flavor, this.name, this.values);
  static FlavorConfig? get instance {
    return _instance;
  }

  static bool isProduction() => _instance!.flavor == Flavor.production;
  static bool isDevelopment() => _instance!.flavor == Flavor.dev;
  static bool isTest() => _instance!.flavor == Flavor.test;
}

class DefaultCredencial {
  final String? url;
  final String user;
  final String password;
  final String? codUsuarioAuditoria;

  DefaultCredencial({
    this.url,
    required this.user,
    required this.password,
    this.codUsuarioAuditoria,
  });
}

class GetNet {
  final String url;
  final String clientId;
  final String clientSecret;
  final String sellerId;

  GetNet(
      {required this.url,
      required this.clientId,
      required this.clientSecret,
      required this.sellerId});
}

class Portal {
  final String url;

  Portal(this.url);
}

class AgendamentoApiData {
  final String url;
  final String user;
  final String password;

  AgendamentoApiData(
      {required this.url, required this.user, required this.password})
      : assert(
          url.isNotEmpty,
        );
}

class NegociacaoDebitoApi {
  final String path;

  NegociacaoDebitoApi({required this.path})
      : assert(
          path.isNotEmpty,
        );
}

class VirtualCardEnv {
  final String urlGetToken;
  final String user;
  final String password;

  VirtualCardEnv(
      {required this.urlGetToken, required this.user, required this.password});
}

class ApiServicos {
  final String url;
  final String user;
  final String password;

  ApiServicos({required this.url, required this.user, required this.password})
      : assert(
          url.isNotEmpty,
        );
}

class HermesPardini {
  final String mobile;
  final String services;
  final String authPass;
  final String siteUf;
  HermesPardini({
    required this.mobile,
    required this.services,
    this.authPass = "U0VOSEEgU0lURSBEQSBVTklNRUQgRk9SVEFMRVpB",
    this.siteUf = "%SITE_UF",
  });
}

class FlavorTEST extends FlavorValues {
  FlavorTEST()
      : super(
          remoteLogEnv: RemoteLogEnv.TEST,
          profilePermissions: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/api/',
            user: '<EMAIL>',
            password: '0aRp66fy6lUGy2oWfyIBp54I35HZ1Eo3calB2AkY',
          ),
          evaluationEnv: EvaluationEnv.TEST,
          paVirtualEnv: PAVirtualEnv.test,
          graphql: DefaultCredencial(
            url: 'https://perfilappsdev.unimedfortaleza.com.br/graphql',
            user: '<EMAIL>',
            password:'\$2b\$12\$9sj.CUqYCvl817GH0Ciuk./8lu9.lZNGd5MnMELapjs9X2Eq1BOE2',
          ),
        );
}

class FlavorDEV extends FlavorValues {
  FlavorDEV()
      : super(
          remoteLogEnv: RemoteLogEnv.DEV,
          profilePermissions: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/api/',
            user: '<EMAIL>',
            password: '0aRp66fy6lUGy2oWfyIBp54I35HZ1Eo3calB2AkY',
          ),
          evaluationEnv: EvaluationEnv.DEV,
          paVirtualEnv: PAVirtualEnv.dev,
          graphql: DefaultCredencial(
            url: 'https://perfilappshmg.unimedfortaleza.com.br/graphql',
            user: '<EMAIL>',
            password:'\$2b\$12\$9sj.CUqYCvl817GH0Ciuk./8lu9.lZNGd5MnMELapjs9X2Eq1BOE2',
          ),
        );
}

class FlavorPROD extends FlavorValues {
  FlavorPROD()
      : super(
          remoteLogEnv: RemoteLogEnv.PROD,
          profilePermissions: DefaultCredencial(
            url: 'https://perfilapps.unimedfortaleza.com.br/api/',
            user: '<EMAIL>',
            password: 'faSl1acDmbbdKtaNRwa8tXrlQEHYKTUqOFkwQz7Z',
          ),
          evaluationEnv: EvaluationEnv.PROD,
          paVirtualEnv: PAVirtualEnv.prod,
          graphql: DefaultCredencial(
            url: 'https://perfilapps.unimedfortaleza.com.br/graphql',
            user: '<EMAIL>',
            password:'\$2b\$12\$BfFcP.ADSqpuPyThjre1r.ux2C2cSY/f7fzG.KSSfbG38yv1Npdvy',
          ),
        );
}
