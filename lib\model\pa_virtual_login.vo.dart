class LoginPAVirtualVO {
  late String nome;
  late String sexo;
  late String cpf;
  late String carteira;
  late String redirecionar;
  late String dataNascimento;
  late String pergunta;
  late String resposta;

  LoginPAVirtualVO(
      {required this.nome,
      required this.sexo,
      required this.cpf,
        required this.carteira,
      required this.redirecionar,
      required this.dataNascimento,
      required this.pergunta,
      required this.resposta});

  LoginPAVirtualVO.fromJson(Map<String, dynamic> json) {
    nome = json['nome'];
    sexo = json['sexo'];
    cpf = json['cpf'];
    carteira = json['carteira'];
    redirecionar = json['redirecionar'];
    dataNascimento = json['dataNascimento'];
    pergunta = json['pergunta'];
    resposta = json['resposta'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['nome'] = nome;
    data['sexo'] = sexo;
    data['carteira'] = carteira;
     data['cpf'] = cpf;
    data['redirecionar'] = redirecionar;
    data['dataNascimento'] = dataNascimento;
    data['pergunta'] = pergunta;
    data['resposta'] = resposta;
    return data;
  }
}
