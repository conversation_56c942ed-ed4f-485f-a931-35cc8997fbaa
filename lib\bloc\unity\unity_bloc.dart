import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:pa_virtual/utils/locator.dart';
import 'package:pavirtual_tablet/bloc/unity/unity_event.dart';
import 'package:pavirtual_tablet/bloc/unity/unity_state.dart';
import 'package:pavirtual_tablet/model/unit_model.dart';
import 'package:pavirtual_tablet/shared/api/config_api.dart';
import 'package:remote_log_elastic/remote_log_elastic.dart';

class UnityBloc extends HydratedBloc<UnityEvent, UnityState> {
  UnityBloc() : super(UnityInitialState());

  UnitModel? _unitySelected;
  UnitModel? get unitySelected => _unitySelected;

  @override
  Stream<UnityState> mapEventToState(
    UnityEvent event,
  ) async* {
    if (event is GetUnitysEvent) {
      try {
        yield UnityLoadingState();
        final api = Locator.instance.get<ConfigApi>();

        final units = await api.getUnits(password: event.password);

        if (units.isEmpty) {
          yield const ErrorUnitState(message: "Lista de unidades vazia");
        }
        // Locator.instance.get<RemoteLog>().setCard(event.carteira);

        // final loginPAVirtualVO = await api.login(carteira: event.carteira);

        // Locator.instance.get<RemoteLog>().setUserId(loginPAVirtualVO.cpf);
        yield LoadUnitDone(units: units);
      } catch (e) {
        yield ErrorUnitState(message: e.toString());
      }
    } else if (event is SelectUnityEvent) {
      _unitySelected = event.unity;
      Locator.instance.get<RemoteLog>().setUserId(event.unity.description);
      yield SelectedUnityState(unitySelected: _unitySelected!);
    }
  }

  @override
  UnityState? fromJson(Map<String, dynamic> json) {
    try {
      _unitySelected = UnitModel.fromJson(json['unitySelected']);

      return SelectedUnityState(unitySelected: _unitySelected!);
    } catch (_) {
      return null;
    }
  }

  @override
  Map<String, dynamic>? toJson(UnityState state) {
    try {
      final json = _unitySelected?.toJson();

      return {'unitySelected': json};
    } catch (_) {
      return null;
    }
  }
}
