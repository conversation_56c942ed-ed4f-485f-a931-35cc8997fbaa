import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:path_provider/path_provider.dart';

import 'package:pavirtual_tablet/screen/main.dart';

import 'package:pavirtual_tablet/shared/flavor_config.dart';
import 'package:pavirtual_tablet/shared/locator.dart';

void main() async {
  FlavorConfig(flavor: Flavor.dev, values: FlavorDEV());
  Locator.setup();
  WidgetsFlutterBinding.ensureInitialized();
  HydratedBloc.storage = await HydratedStorage.build(
    storageDirectory: await getTemporaryDirectory(),
  );

  await Firebase.initializeApp();

  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

  runApp(const PaTablet());
}
